// System detection and auto-download functionality
class SystemDetector {
  constructor() {
    this.systemData = {
      windows: {
        name: "Windows",
        specs: "Windows 10/11 (64-bit)",
        size: "120MB",
        downloadUrl: "#", // Replace with actual download URL
        iconPath: "M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-12.9-1.801",
        iconViewBox: "0 0 24 24"
      },
      macos: {
        name: "macOS",
        specs: "macOS 11.0 or later",
        size: "115MB",
        downloadUrl: "#", // Replace with actual download URL
        iconPath: "M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701",
        iconViewBox: "0 0 24 24"
      },
      linux: {
        name: "Linux",
        specs: "Ubuntu, Debian, Fedora",
        size: "110MB",
        downloadUrl: "#", // Replace with actual download URL
        iconPath: "M14.923 8.080c-0.025 0.072-0.141 0.061-0.207 0.082-0.059 0.031-0.107 0.085-0.175 0.085-0.062 0-0.162-0.025-0.17-0.085-0.012-0.082 0.11-0.166 0.187-0.166 0.050-0.024 0.108-0.037 0.169-0.037 0.056 0 0.109 0.011 0.157 0.032l-0.003-0.001c0.022 0.009 0.038 0.030 0.038 0.055 0 0.003-0 0.005-0.001 0.008l0-0v0.025h0.004zM15.611 8.080v-0.027c-0.008-0.025 0.016-0.052 0.036-0.062 0.046-0.020 0.1-0.032 0.157-0.032 0.061 0 0.119 0.014 0.17 0.038l-0.002-0.001c0.079 0 0.2 0.084 0.187 0.169-0.007 0.061-0.106 0.082-0.169 0.082-0.069 0-0.115-0.054-0.176-0.085-0.065-0.023-0.182-0.010-0.204-0.081zM16.963 10.058c-0.532 0.337-1.161 0.574-1.835 0.666l-0.024 0.003c-0.606-0.035-1.157-0.248-1.607-0.588l0.007 0.005c-0.192-0.167-0.35-0.335-0.466-0.419-0.205-0.167-0.18-0.416-0.092-0.416 0.136 0.020 0.161 0.167 0.249 0.25 0.12 0.082 0.269 0.25 0.45 0.416 0.397 0.328 0.899 0.541 1.45 0.583l0.009 0.001c0.654-0.057 1.249-0.267 1.763-0.592l-0.016 0.010c0.244-0.169 0.556-0.417 0.81-0.584 0.195-0.17 0.186-0.334 0.349-0.334 0.16 0.020 0.043 0.167-0.184 0.415-0.246 0.188-0.527 0.381-0.818 0.56l-0.044 0.025z",
        iconViewBox: "0 0 32 32"
      }
    };
  }

  detectOS() {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();
    
    if (userAgent.includes('win') || platform.includes('win')) {
      return 'windows';
    } else if (userAgent.includes('mac') || platform.includes('mac')) {
      return 'macos';
    } else if (userAgent.includes('linux') || userAgent.includes('x11')) {
      return 'linux';
    } else {
      return 'unknown';
    }
  }

  createIcon(systemInfo) {
    return `<svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-violet-400" fill="currentColor" viewBox="${systemInfo.iconViewBox}">
      <title>${systemInfo.name.toLowerCase()}</title>
      <path d="${systemInfo.iconPath}"/>
    </svg>`;
  }

  updateSystemElements(systemInfo) {
    const elements = {
      icon: document.getElementById('current-system-icon'),
      name: document.getElementById('current-system-name'),
      specs: document.getElementById('current-system-specs'),
      link: document.getElementById('current-system-link'),
      size: document.getElementById('current-system-size')
    };

    // Check if all elements exist
    if (Object.values(elements).some(el => !el)) {
      console.warn('Some system detection elements are missing from the DOM');
      return false;
    }

    // Update elements
    elements.icon.innerHTML = this.createIcon(systemInfo);
    elements.name.textContent = systemInfo.name;
    elements.specs.textContent = systemInfo.specs;
    elements.link.href = systemInfo.downloadUrl;
    elements.size.textContent = systemInfo.size;

    return true;
  }

  showCurrentSystemDownload() {
    const detectedOS = this.detectOS();
    const systemInfo = this.systemData[detectedOS];
    
    if (systemInfo && this.updateSystemElements(systemInfo)) {
      // Show the current system download section
      const downloadSection = document.getElementById('current-system-download');
      if (downloadSection) {
        downloadSection.classList.remove('hidden');
      }
    }
  }

  init() {
    // Run the detection when the page loads
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.showCurrentSystemDownload());
    } else {
      this.showCurrentSystemDownload();
    }
  }
}

// Initialize the system detector
const systemDetector = new SystemDetector();
systemDetector.init();
